export interface Video {
  id: number;
  video_id: string;
  filename: string;
  original_filename: string;
  s3_key: string;
  s3_uri: string;
  status: 'uploading' | 'uploaded' | 'processing' | 'indexed' | 'failed';
  file_size?: number;
  duration?: number;
  width?: number;
  height?: number;
  fps?: number;
  indexed_at?: string;
  summary?: string;
  risk_score?: number;
  created_at: string;
  updated_at?: string;
}

export interface SearchResult {
  video_id: string;
  segment_id: number;
  timestamp: string;
  start_sec: number;
  duration: number;
  similarity_score: number;
  clip_url?: string;
  processed_clip_url?: string;
  processing_status?: string;
  processing_task_id?: string;
  safety_report?: {
    summary: string;
    risk_score?: number;
    model: string;
    clip_s3_key?: string;
    analysis_available?: boolean;
  };
  metadata: Record<string, any>;
}

export interface SearchRequest {
  query: string;
  top_k: number;
  model: 'nova-premier' | 'marengo';
}

export interface SearchResponse {
  query: string;
  results: SearchResult[];
  total: number;
  model_used: 'nova-premier' | 'marengo';
}

export interface Job {
  job_id: string;
  video_id: string;
  job_type: string;
  status: string;
  progress: number;
  message?: string;
  error?: string;
  created_at?: string;
  completed_at?: string;
}

export interface Rating {
  id: number;
  query: string;
  video_id: string;
  segment_id: number;
  rating: number;
  model_type: 'nova-premier' | 'marengo';
  similarity_score: number;
  created_at: string;
}

export interface ProcessingOptions {
  enable_labeling: boolean;
  enable_pii_blur: boolean;
  blur_faces: boolean;
  blur_plates: boolean;
  enable_tracking: boolean;
  confidence_threshold: number;
  blur_intensity: number;
}

export interface WebSocketMessage {
  type: string;
  [key: string]: any;
}
