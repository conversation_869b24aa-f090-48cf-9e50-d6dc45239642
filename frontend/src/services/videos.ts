import { Video } from '../types';
import api from './api';

export const videosService = {
  uploadVideo: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/videos/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  uploadFromYoutube: async (youtubeUrl: string) => {
    const response = await api.post('/videos/upload-youtube', {
      youtube_url: youtubeUrl,
    });
    return response.data;
  },

  listVideos: async (params?: {
    skip?: number;
    limit?: number;
    status_filter?: string;
    model_filter?: string;
  }) => {
    const response = await api.get<Video[]>('/videos', { params });
    return response.data;
  },

  getVideo: async (videoId: string) => {
    const response = await api.get<Video>(`/videos/${videoId}`);
    return response.data;
  },

  deleteVideo: async (videoId: string) => {
    const response = await api.delete(`/videos/${videoId}`);
    return response.data;
  },

  indexVideo: async (videoId: string) => {
    const response = await api.post(`/videos/${videoId}/index`);
    return response.data;
  },

  getVideoSummary: async (videoId: string) => {
    const response = await api.get(`/videos/${videoId}/summary`);
    return response.data;
  },

  getStreamUrl: async (videoId: string) => {
    const response = await api.get(`/videos/${videoId}/stream`);
    return response.data;
  },
};
