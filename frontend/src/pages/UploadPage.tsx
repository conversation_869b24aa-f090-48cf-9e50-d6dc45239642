import { CheckCircle, Upload, XCircle } from 'lucide-react';
import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Layout } from '../components/common/Layout';
import { LoadingSpinner } from '../components/common/LoadingSpinner';
import { videosService } from '../services/videos';

export const UploadPage: React.FC = () => {
  const [uploading, setUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');
  const [uploadMethod, setUploadMethod] = useState<'file' | 'youtube'>('file');
  const [youtubeUrl, setYoutubeUrl] = useState('');
  
  // Size limit constants
  const MAX_FILE_SIZE_GB = 2;
  const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_GB * 1024 * 1024 * 1024;
  
  const handleYoutubeUpload = async () => {
    if (!youtubeUrl.trim()) return;

    setUploading(true);
    setUploadStatus('idle');
    setMessage('');

    try {
      const response = await videosService.uploadFromYoutube(youtubeUrl);
      setUploadStatus('success');
      setMessage(`YouTube video download started! Video ID: ${response.video_id}`);
      setYoutubeUrl('');
    } catch (error: any) {
      setUploadStatus('error');
      setMessage(error.response?.data?.detail || 'YouTube download failed');
    } finally {
      setUploading(false);
    }
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    setUploading(true);
    setUploadStatus('idle');
    setMessage(`Uploading ${acceptedFiles.length} video(s)...`);

    try {
      // Upload all files in parallel
      const uploadPromises = acceptedFiles.map(file =>
        videosService.uploadVideo(file)
      );

      const responses = await Promise.all(uploadPromises);
      setUploadStatus('success');
      setMessage(`Successfully uploaded ${responses.length} video(s)!`);
    } catch (error: any) {
      setUploadStatus('error');
      setMessage(error.response?.data?.detail || 'Upload failed');
    } finally {
      setUploading(false);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive, fileRejections } = useDropzone({
    onDrop,
    accept: {
      'video/*': ['.mp4', '.avi', '.mov', '.mkv']
    },
    multiple: true,
    maxSize: MAX_FILE_SIZE_BYTES,
    disabled: uploading,
    onDropRejected: (rejections) => {
      const errors = rejections.map(r => {
        if (r.errors[0]?.code === 'file-too-large') {
          return `${r.file.name}: File too large (max ${MAX_FILE_SIZE_GB}GB)`;
        }
        return `${r.file.name}: ${r.errors[0]?.message}`;
      });
      setUploadStatus('error');
      setMessage(errors.join(', '));
    }
  });

  return (
    <Layout>
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">Upload Videos</h1>
            <p className="mt-2 text-sm text-gray-700">
              Upload one or multiple video files (up to {MAX_FILE_SIZE_GB}GB each)
            </p>
            <div className="mt-2 flex items-center gap-2 text-xs text-gray-600">
              <span className="inline-flex items-center px-2 py-1 rounded-md bg-blue-50 text-blue-700 font-medium">
                Max {MAX_FILE_SIZE_GB}GB per file
              </span>
              <span className="inline-flex items-center px-2 py-1 rounded-md bg-green-50 text-green-700 font-medium">
                Supported: MP4, AVI, MOV, MKV
              </span>
              <span className="inline-flex items-center px-2 py-1 rounded-md bg-purple-50 text-purple-700 font-medium">
                Multiple uploads supported
              </span>
            </div>
          </div>
        </div>

        <div className="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Upload Section - Left Side (2/3 width) */}
          <div className="lg:col-span-2">
            {/* Upload Method Tabs */}
            <div className="mb-6">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                  <button
                    onClick={() => setUploadMethod('file')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      uploadMethod === 'file'
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Upload from File
                  </button>
                  <button
                    onClick={() => setUploadMethod('youtube')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      uploadMethod === 'youtube'
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Download from YouTube
                  </button>
                </nav>
              </div>
            </div>

            {/* File Upload */}
            {uploadMethod === 'file' && (
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-12 text-center cursor-pointer transition-colors ${
                  isDragActive
                    ? 'border-primary-500 bg-primary-50'
                    : 'border-gray-300 hover:border-gray-400'
                } ${uploading ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                <input {...getInputProps()} />
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-sm font-medium text-gray-900">
                  {isDragActive ? 'Drop the video here' : 'Drag and drop a video file'}
                </p>
                <p className="mt-1 text-xs text-gray-500">
                  or click to select a file
                </p>
                <p className="mt-2 text-xs text-gray-500">
                  Supported formats: MP4, AVI, MOV, MKV (Max 2GB)
                </p>
              </div>
            )}

            {/* YouTube Upload */}
            {uploadMethod === 'youtube' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    YouTube URL
                  </label>
                  <input
                    type="url"
                    value={youtubeUrl}
                    onChange={(e) => setYoutubeUrl(e.target.value)}
                    placeholder="https://www.youtube.com/watch?v=..."
                    disabled={uploading}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
                  />
                </div>
                <button
                  onClick={handleYoutubeUpload}
                  disabled={uploading || !youtubeUrl.trim()}
                  className="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Download & Index Video
                </button>
                <p className="text-xs text-gray-500">
                  💡 The video will be downloaded from YouTube and processed automatically
                </p>
              </div>
            )}

            {/* Upload Status */}
            {uploading && (
              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center">
                  <LoadingSpinner size="sm" />
                  <span className="ml-3 text-sm text-blue-800">
                    Uploading video... This may take a few moments.
                  </span>
                </div>
              </div>
            )}

            {uploadStatus === 'success' && (
              <div className="mt-6 p-4 bg-green-50 rounded-lg">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="ml-3 text-sm text-green-800">{message}</span>
                </div>
                <p className="mt-2 text-xs text-green-700">
                  The video is being processed in the background. Check the Videos page for status.
                </p>
              </div>
            )}

            {uploadStatus === 'error' && (
              <div className="mt-6 p-4 bg-red-50 rounded-lg">
                <div className="flex items-center">
                  <XCircle className="h-5 w-5 text-red-600" />
                  <span className="ml-3 text-sm text-red-800">{message}</span>
                </div>
              </div>
            )}
          </div>

          {/* Instructions - Right Side (1/3 width) */}
          <div className="lg:col-span-1">
            <div className="bg-gray-50 rounded-lg p-6 sticky top-8">
              <h3 className="text-sm font-medium text-gray-900 mb-3">
                What happens after upload?
              </h3>
              <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
                <li>Video is uploaded to AWS S3</li>
                <li>Metadata is extracted (duration, resolution, fps)</li>
                <li>Embeddings are generated using Nova Premier & TwelveLabs Marengo</li>
                <li>Video is indexed in Weaviate for semantic search</li>
                <li>Safety summary is generated using Nova Premier</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};
