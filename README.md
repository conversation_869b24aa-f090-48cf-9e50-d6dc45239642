# 🎥 Samsara Video Analysis Platform

A video semantic search application using TwelveLabs Marengo model and AWS Titan Image Embedding for video indexing, natural language queries, object labeling via AWS Rekognition, and PII blurring capabilities.

## 🏗️ Project Overview

Samsara POC is a comprehensive video analysis platform that enables:
- **Video Upload & Processing**: Direct upload and YouTube URL support
- **AI-Powered Search**: Natural language video search using semantic embeddings
- **Object Detection**: YOLOv8-based object detection and tracking
- **Safety Analysis**: Automated safety reports with risk scoring
- **PII Protection**: Face and license plate blurring capabilities

## 🛠️ Technology Stack

### Backend
- **FastAPI** - Modern async web framework
- **PostgreSQL** - Relational database with Alembic migrations
- **Weaviate** - Vector database for embeddings
- **Redis** - Caching and message broker
- **Celery** - Background task processing

### Frontend
- **React 18** + TypeScript
- **TailwindCSS** for styling
- **React Query** for data fetching

### AI/ML Services
- **YOLOv8** - Object detection and tracking
- **AWS Bedrock** - Nova Premier & Marengo embeddings
- **AWS Rekognition** - Object labeling
- **MediaPipe** - Face detection for PII blurring

## 📁 Repository Structure

```
samsara-poc-main/
├── README.md              # This documentation
├── docker-compose.yml     # Full stack orchestration
├── .env                   # Environment variables
├── backend/               # FastAPI application
│   ├── app/               # Application code
│   ├── alembic/           # Database migrations
│   ├── requirements.txt   # Python dependencies
│   └── Dockerfile
└── frontend/              # React application
    ├── src/               # React TypeScript code
    ├── package.json       # Node dependencies
    └── Dockerfile
```

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- AWS Account with Bedrock access
- 8GB+ RAM recommended

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd samsara-poc-main
```

2. **Configure environment**
```bash
# Create .env file with your AWS credentials
cp .env.example .env
# Edit .env with your AWS credentials and S3 bucket
```

3. **Start the application**
```bash
docker compose up -d
```

4. **Run database migrations** (Required after removing volumes)
```bash
docker exec samsara-backend alembic upgrade head
```

### Access Points
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

### Default Credentials
- **Username**: `admin`
- **Password**: `minfy2025`

## 🗄️ Database Migrations

When you remove Docker volumes and rebuild, you need to run migrations manually:

```bash
# Run database migrations
docker exec samsara-backend alembic upgrade head

# Check migration status
docker exec samsara-backend alembic current

# View migration history
docker exec samsara-backend alembic history
```

## 🔍 Troubleshooting

### Common Issues

**Database table errors after volume reset:**
```bash
# Solution: Run migrations
docker exec samsara-backend alembic upgrade head
```

**Check service logs:**
```bash
# Backend logs
docker logs samsara-backend --tail 50

# Celery worker logs
docker logs samsara-celery-worker --tail 50

# Frontend logs
docker logs samsara-frontend --tail 50
```

**Reset everything:**
```bash
# Stop and remove all containers, volumes, and images
docker compose down -v --rmi all

# Rebuild and start
docker compose up -d

# Run migrations
docker exec samsara-backend alembic upgrade head
```

## 📄 License

Proprietary - Samsara Internal Use